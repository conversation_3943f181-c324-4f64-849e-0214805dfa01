import numpy as np
import math
from typing import List
import os
import jieba
import jieba.posseg as pseg
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# 数据集路径
path = 'C:/Users/<USER>/Desktop/资料/大三上/信息检索/c4166-main/SougoCS_all_classes/jiaoyu/'
files = [f for f in os.listdir(path) if f.endswith('.txt')]

# 极度限制文档数量以减少计算量
MAX_DOCS = 50  # 进一步减少
files = files[:MAX_DOCS]

docs = []
for f in files:
    with open(os.path.join(path, f), encoding='utf-8') as file:
        content = file.read()
        # 更严格限制文档长度
        if len(content) > 2000:
            content = content[:2000]
        docs.append(content)

# 添加核心领域词
jieba.add_word("高考改革")
jieba.add_word("义务教育")
jieba.add_word("素质教育")
jieba.add_word("职业教育")

# 简化停用词表
stopwords = {'的', '我', '想', '知道', '相关', '是', '在', '和', '了', '有', '这', '那', '就', '都', '要', '会', '能', '可以'}

def extract_keywords(query: str) -> str:
    """提取查询中的关键词（名词和动词，去除停用词）"""
    words = pseg.cut(query)
    keywords = [w for w, flag in words if flag.startswith(('n', 'v')) and w not in stopwords and len(w) > 1]
    return ' '.join(keywords)

class SimpleBM25:
    """简化版BM25，减少预计算以节省内存"""
    def __init__(self, corpus: List[str], k1=1.5, b=0.75):
        self.k1, self.b = k1, b
        self.corpus = [jieba.lcut(doc) for doc in corpus]
        self.N = len(self.corpus)
        self.avdl = sum(len(doc) for doc in self.corpus) / self.N
        
        # 只计算必要的统计信息
        self.doc_lengths = [len(doc) for doc in self.corpus]
        
        # 计算词汇表和df
        vocab = set()
        for doc in self.corpus:
            vocab.update(doc)
        
        self.df = {}
        for word in vocab:
            self.df[word] = sum(1 for doc in self.corpus if word in doc)
        
        # 预计算idf
        self.idf = {w: math.log((self.N - self.df[w] + 0.5) / (self.df[w] + 0.5)) for w in self.df}

    def score(self, query: str, doc_idx: int) -> float:
        query_words = jieba.lcut(query)
        doc = self.corpus[doc_idx]
        doc_len = self.doc_lengths[doc_idx]
        
        # 实时计算词频以节省内存
        doc_freq = Counter(doc)
        
        score = 0.0
        for w in query_words:
            if w not in self.idf:
                continue
            tf = doc_freq.get(w, 0)
            K = self.k1 * (1 - self.b + self.b * doc_len / self.avdl)
            score += self.idf[w] * (tf * (self.k1 + 1)) / (tf + K)
        return score

    def get_scores(self, query: str) -> np.ndarray:
        scores = [self.score(query, i) for i in range(self.N)]
        return np.array(scores)

def tfidf_rank(q):
    q_vec = tfidf.transform([q])
    scores = cosine_similarity(q_vec, X).flatten()
    return scores

def top_k_indices(scores, k=10):
    return np.argsort(scores)[::-1][:k]

def prf_at_k(pred, gold, k=10):
    pred = set(pred[:k])
    gold = set(gold)
    p = len(pred & gold) / k if k > 0 else 0
    r = len(pred & gold) / max(len(gold), 1)
    f = 0 if p + r == 0 else 2 * p * r / (p + r)
    return p, r, f

if __name__ == "__main__":
    print(f"加载了 {len(docs)} 个文档")
    
    # 1. 预处理文档
    print("正在预处理文档...")
    docs_for_tfidf = [' '.join(jieba.lcut(d)) for d in docs]

    # 2. 最小查询集
    raw_queries = [
        "高考改革相关信息",
        "义务教育资源均衡",
        "素质教育全面发展"
    ]
    queries = [extract_keywords(q) for q in raw_queries]
    print(f"处理 {len(queries)} 个查询")

    # 3. 生成简化伪标签
    print("生成伪标签...")
    bm25 = SimpleBM25(docs, k1=1.5, b=0.75)
    labels = []
    for q in queries:
        q_words = set(jieba.lcut(q))
        # 更宽松的阈值
        gold = [i for i, doc in enumerate(bm25.corpus)
                if len(set(doc) & q_words) >= 1]
        labels.append(gold)

    # 4. TF-IDF（限制特征数量）
    print("训练TF-IDF...")
    tfidf = TfidfVectorizer(tokenizer=jieba.lcut, lowercase=False, max_features=1000)
    X = tfidf.fit_transform(docs_for_tfidf)

    # 5. 最简BM25参数测试（只测试2个参数组合）
    print("\nBM25 快速测试结果：")
    param_combinations = [(1.2, 0.75), (1.8, 0.75)]
    
    for k1, b in param_combinations:
        bm25_temp = SimpleBM25(docs, k1=k1, b=b)
        avg = np.zeros(3)
        for q, gold in zip(queries, labels):
            scores = bm25_temp.get_scores(q)
            pred = top_k_indices(scores)
            p, r, f = prf_at_k(pred, gold)
            avg += np.array([p, r, f])
        avg /= len(queries)
        print(f"k1={k1:.1f}, b={b:.2f}: P@10={avg[0]:.3f}, R@10={avg[1]:.3f}, F1@10={avg[2]:.3f}")

    # 6. TF-IDF 结果
    print("\nTF-IDF Results:")
    avg = np.zeros(3)
    for q, gold in zip(queries, labels):
        scores = tfidf_rank(q)
        pred = top_k_indices(scores)
        p, r, f = prf_at_k(pred, gold)
        avg += np.array([p, r, f])
    avg /= len(queries)
    print("P@10=%.3f, R@10=%.3f, F1@10=%.3f" % tuple(avg))
    
    print("\n超轻量化版本运行完成！")
