import numpy as np
import math
from typing import List
import os
import jieba
import jieba.posseg as pseg
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
from multiprocessing import Pool

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 数据集路径
path = 'C:/Users/<USER>/Desktop/资料/大三上/信息检索/c4166-main/SougoCS_all_classes/jiaoyu/'
files = [f for f in os.listdir(path) if f.endswith('.txt')]
docs = []
for f in files:
    with open(os.path.join(path, f), encoding='utf-8') as file:
        docs.append(file.read())

# 添加领域词
jieba.add_word("高考改革")
jieba.add_word("义务教育")
jieba.add_word("双一流")
jieba.add_word("乡村教师")
jieba.add_word("素质教育")
jieba.add_word("减负政策")
jieba.add_word("校园安全")
jieba.add_word("学前教育")
jieba.add_word("在线教育")
jieba.add_word("职业教育")

# 加载停用词表
stopwords = set()
with open('stopwords', 'r', encoding='utf-8') as f:
    for line in f:
        stopwords.add(line.strip())


def extract_keywords(query: str) -> str:
    """提取查询中的关键词（名词和动词，去除停用词）"""
    words = pseg.cut(query)
    keywords = [w for w, flag in words if flag.startswith(('n', 'v')) and w not in stopwords]
    return ' '.join(keywords)


class MyBM25:
    def __init__(self, corpus: List[str], k1=1.5, b=0.75):
        self.k1, self.b = k1, b
        self.corpus = [jieba.lcut(doc) for doc in corpus]
        self.N = len(self.corpus)
        self.avdl = sum(len(doc) for doc in self.corpus) / self.N
        self.doc_freqs = [dict(Counter(doc)) for doc in self.corpus]  # 预计算词频
        self.doc_lengths = [len(doc) for doc in self.corpus]  # 预计算文档长度

        # 计算 df
        df = {}
        for doc in self.corpus:
            for w in set(doc):
                df[w] = df.get(w, 0) + 1
        self.df = df
        self.idf = {w: math.log((self.N - df[w] + 0.5) / (df[w] + 0.5)) for w in df}

    def score(self, query: str, doc_idx: int) -> float:
        query_words = jieba.lcut(query)
        doc_len = self.doc_lengths[doc_idx]
        freq = self.doc_freqs[doc_idx]
        score = 0.0
        for w in query_words:
            if w not in self.idf:
                continue
            tf = freq.get(w, 0)
            K = self.k1 * (1 - self.b + self.b * doc_len / self.avdl)
            score += self.idf[w] * (tf * (self.k1 + 1)) / (tf + K)
        return score

    def get_scores(self, query: str) -> np.ndarray:
        with Pool() as pool:
            scores = pool.starmap(self.score, [(query, i) for i in range(self.N)])
        return np.array(scores)


def tfidf_rank(q):
    q_vec = tfidf.transform([q])
    scores = cosine_similarity(q_vec, X).flatten()
    return scores


def top_k_indices(scores, k=10):
    return np.argsort(scores)[::-1][:k]


def prf_at_k(pred, gold, k=10):
    pred = set(pred[:k])
    gold = set(gold)
    p = len(pred & gold) / k
    r = len(pred & gold) / max(len(gold), 1)
    f = 0 if p + r == 0 else 2 * p * r / (p + r)
    return p, r, f


if __name__ == "__main__":
    # 1. 预处理文档
    docs_for_tfidf = [' '.join(jieba.lcut(d)) for d in docs]

    # 2. 自然语言查询
    raw_queries = [
        "我想知道高考改革相关信息",
        "义务教育资源均衡的措施有哪些",
        "在线教育在后疫情时代的技术支持",
        "乡村教师培训计划如何实施",
        "素质教育如何促进学生全面发展",
        "小学课程减负政策的具体内容",
        "职业教育如何培养学生实践技能",
        "校园安全应急预案的管理办法",
        "双一流高校建设的科研支持",
        "学前教育师资培养的质量保障"
    ]
    queries = [extract_keywords(q) for q in raw_queries]

    # 3. 生成伪标签
    bm25 = MyBM25(docs, k1=1.5, b=0.75)
    labels = []
    for q in queries:
        q_words = set(jieba.lcut(q))
        threshold = len(q_words) * 0.7  # 降低阈值以适应长句
        gold = [i for i, doc in enumerate(bm25.corpus)
                if len(set(doc) & q_words) >= threshold and sum(bm25.doc_freqs[i].get(w, 0) for w in q_words) >= 2]
        labels.append(gold)

    # 4. TF-IDF
    tfidf = TfidfVectorizer(tokenizer=jieba.lcut, lowercase=False)
    X = tfidf.fit_transform(docs_for_tfidf)

    # 5. BM25 参数调优
    print("\nBM25 参数调优结果：")
    k1_values = [0.2, 1.0, 3.0, 7.0, 10.0]
    b_values = [0.1, 0.3, 0.5, 0.75, 1.0]
    f1_matrix = np.zeros((len(k1_values), len(b_values)))

    for i, k1 in enumerate(k1_values):
        for j, b in enumerate(b_values):
            bm25 = MyBM25(docs, k1=k1, b=b)
            avg = np.zeros(3)
            for q, gold in zip(queries, labels):
                scores = bm25.get_scores(q)
                pred = top_k_indices(scores)
                p, r, f = prf_at_k(pred, gold)
                avg += np.array([p, r, f])
            avg /= len(queries)
            f1_matrix[i, j] = avg[2]
            print(f"k1={k1:.1f}, b={b:.2f}: P@10={avg[0]:.3f}, R@10={avg[1]:.3f}, F1@10={avg[2]:.3f}")

    # 6. 绘制热力图
    fig, ax = plt.subplots()
    im = ax.imshow(f1_matrix, cmap='viridis')
    ax.set_xticks(np.arange(len(b_values)))
    ax.set_yticks(np.arange(len(k1_values)))
    ax.set_xticklabels(b_values)
    ax.set_yticklabels(k1_values)
    ax.set_xlabel('b 值')
    ax.set_ylabel('k1 值')
    ax.set_title('k1 和 b 对平均 F1@10 的影响（热力图）')
    for i in range(len(k1_values)):
        for j in range(len(b_values)):
            text = ax.text(j, i, f"{f1_matrix[i, j]:.3f}", ha="center", va="center", color="w")
    cbar = ax.figure.colorbar(im, ax=ax)
    cbar.ax.set_ylabel("平均 F1@10", rotation=-90, va="bottom")
    plt.savefig('heatmap.png')
    plt.close()

    # 7. TF-IDF 结果
    print("\nTF-IDF Results:")
    avg = np.zeros(3)
    for q, gold in zip(queries, labels):
        scores = tfidf_rank(q)
        pred = top_k_indices(scores)
        p, r, f = prf_at_k(pred, gold)
        avg += np.array([p, r, f])
    avg /= len(queries)
    print("P@10=%.3f, R@10=%.3f, F1@10=%.3f" % tuple(avg))