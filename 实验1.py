import numpy as np
import math
from typing import List
import os
import jieba
import jieba.posseg as pseg
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
# 移除多进程以减少开销

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 数据集路径
path = 'C:/Users/<USER>/Desktop/资料/大三上/信息检索/c4166-main/SougoCS_all_classes/jiaoyu/'
files = [f for f in os.listdir(path) if f.endswith('.txt')]

# 限制文档数量以减少计算量
MAX_DOCS = 100  # 可根据需要调整
files = files[:MAX_DOCS]

docs = []
for f in files:
    with open(os.path.join(path, f), encoding='utf-8') as file:
        content = file.read()
        # 限制文档长度以减少内存占用
        if len(content) > 5000:
            content = content[:5000]
        docs.append(content)

# 添加领域词
jieba.add_word("高考改革")
jieba.add_word("义务教育")
jieba.add_word("双一流")
jieba.add_word("乡村教师")
jieba.add_word("素质教育")
jieba.add_word("减负政策")
jieba.add_word("校园安全")
jieba.add_word("学前教育")
jieba.add_word("在线教育")
jieba.add_word("职业教育")

# 加载停用词表（需准备 stopwords.txt）
stopwords = set()
try:
    with open('stopwords', 'r', encoding='utf-8') as f:
        for line in f:
            stopwords.add(line.strip())
except FileNotFoundError:
    print("Warning: stopwords.txt not found. Using a minimal set of stopwords.")
    stopwords = {'的', '我', '想', '知道', '相关', '是', '在', '和', '了'}


def extract_keywords(query: str) -> str:
    """提取查询中的关键词（名词和动词，去除停用词）"""
    words = pseg.cut(query)
    keywords = [w for w, flag in words if flag.startswith(('n', 'v')) and w not in stopwords]
    return ' '.join(keywords)


class MyBM25:
    def __init__(self, corpus: List[str], k1=1.5, b=0.85, tokenized_corpus=None):
        self.k1, self.b = k1, b
        # 复用已分词的语料库以避免重复分词
        if tokenized_corpus is not None:
            self.corpus = tokenized_corpus
        else:
            self.corpus = [jieba.lcut(doc) for doc in corpus]

        self.N = len(self.corpus)
        self.avdl = sum(len(doc) for doc in self.corpus) / self.N
        self.doc_freqs = [dict(Counter(doc)) for doc in self.corpus]
        self.doc_lengths = [len(doc) for doc in self.corpus]

        # 计算 df 和 idf
        df = {}
        for doc in self.corpus:
            for w in set(doc):
                df[w] = df.get(w, 0) + 1
        self.df = df
        self.idf = {w: math.log((self.N - df[w] + 0.5) / (df[w] + 0.5)) for w in df}

    def score(self, query: str, doc_idx: int) -> float:
        query_words = jieba.lcut(query)
        doc_len = self.doc_lengths[doc_idx]
        freq = self.doc_freqs[doc_idx]
        score = 0.0
        for w in query_words:
            if w not in self.idf:
                continue
            tf = freq.get(w, 0)
            K = self.k1 * (1 - self.b + self.b * doc_len / self.avdl)
            score += self.idf[w] * (tf * (self.k1 + 1)) / (tf + K)
        return score

    def get_scores(self, query: str) -> np.ndarray:
        # 移除多进程，使用简单循环以减少开销
        scores = [self.score(query, i) for i in range(self.N)]
        return np.array(scores)


def tfidf_rank(q):
    q_vec = tfidf.transform([q])
    scores = cosine_similarity(q_vec, X).flatten()
    return scores


def top_k_indices(scores, k=10):
    return np.argsort(scores)[::-1][:k]


def prf_at_k(pred, gold, k=10):
    pred = set(pred[:k])
    gold = set(gold)
    p = len(pred & gold) / k
    r = len(pred & gold) / max(len(gold), 1)
    f = 0 if p + r == 0 else 2 * p * r / (p + r)
    return p, r, f


if __name__ == "__main__":
    print(f"加载了 {len(docs)} 个文档")

    # 1. 预处理文档（一次性分词，避免重复计算）
    print("正在预处理文档...")
    tokenized_docs = [jieba.lcut(d) for d in docs]
    docs_for_tfidf = [' '.join(tokens) for tokens in tokenized_docs]

    # 2. 简化查询集（减少查询数量以加快测试）
    raw_queries = [
        "我想知道高考改革相关信息",
        "义务教育资源均衡的措施有哪些",
        "在线教育在后疫情时代的技术支持",
        "素质教育如何促进学生全面发展",
        "职业教育如何培养学生实践技能"
    ]
    queries = [extract_keywords(q) for q in raw_queries]
    print(f"处理 {len(queries)} 个查询")

    # 3. 生成伪标签（复用已分词的文档）
    print("生成伪标签...")
    bm25 = MyBM25(docs, k1=1.5, b=0.75, tokenized_corpus=tokenized_docs)
    labels = []
    for q in queries:
        q_words = set(jieba.lcut(q))
        threshold = max(1, len(q_words) * 0.5)  # 降低阈值
        gold = [i for i, doc in enumerate(bm25.corpus)
                if len(set(doc) & q_words) >= threshold]
        labels.append(gold)

    # 4. TF-IDF
    print("训练TF-IDF...")
    tfidf = TfidfVectorizer(tokenizer=jieba.lcut, lowercase=False, max_features=5000)  # 限制特征数量
    X = tfidf.fit_transform(docs_for_tfidf)

    # 5. 简化的BM25参数调优（减少参数组合）
    print("\nBM25 参数调优结果：")
    k1_values = [1.0, 1.5, 2.0]  # 减少参数值
    b_values = [0.5, 0.75, 1.0]  # 减少参数值
    f1_matrix = np.zeros((len(k1_values), len(b_values)))

    for i, k1 in enumerate(k1_values):
        for j, b in enumerate(b_values):
            # 复用已分词的文档以避免重复分词
            bm25_temp = MyBM25(docs, k1=k1, b=b, tokenized_corpus=tokenized_docs)
            avg = np.zeros(3)
            for q, gold in zip(queries, labels):
                scores = bm25_temp.get_scores(q)
                pred = top_k_indices(scores)
                p, r, f = prf_at_k(pred, gold)
                avg += np.array([p, r, f])
            avg /= len(queries)
            f1_matrix[i, j] = avg[2]
            print(f"k1={k1:.1f}, b={b:.2f}: P@10={avg[0]:.3f}, R@10={avg[1]:.3f}, F1@10={avg[2]:.3f}")

    # 6. 绘制热力图（可选，注释掉以进一步减少开销）
    try:
        fig, ax = plt.subplots(figsize=(6, 4))  # 减小图片尺寸
        im = ax.imshow(f1_matrix, cmap='viridis')
        ax.set_xticks(np.arange(len(b_values)))
        ax.set_yticks(np.arange(len(k1_values)))
        ax.set_xticklabels(b_values)
        ax.set_yticklabels(k1_values)
        ax.set_xlabel('b 值')
        ax.set_ylabel('k1 值')
        ax.set_title('k1 和 b 对平均 F1@10 的影响')
        for i in range(len(k1_values)):
            for j in range(len(b_values)):
                text = ax.text(j, i, f"{f1_matrix[i, j]:.3f}", ha="center", va="center", color="w")
        cbar = ax.figure.colorbar(im, ax=ax)
        cbar.ax.set_ylabel("平均 F1@10", rotation=-90, va="bottom")
        plt.savefig('heatmap.png', dpi=100)  # 降低DPI
        plt.close()
        print("热力图已保存为 heatmap.png")
    except Exception as e:
        print(f"绘图失败: {e}")

    # 7. TF-IDF 结果
    print("\nTF-IDF Results:")
    avg = np.zeros(3)
    for q, gold in zip(queries, labels):
        scores = tfidf_rank(q)
        pred = top_k_indices(scores)
        p, r, f = prf_at_k(pred, gold)
        avg += np.array([p, r, f])
    avg /= len(queries)
    print("P@10=%.3f, R@10=%.3f, F1@10=%.3f" % tuple(avg))

    print("\n轻量化优化完成！")